'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Star, Eye, Download, ArrowRight, ShoppingCart, MessageCircle, Heart, ExternalLink, Zap, IndianRupee } from 'lucide-react';
import { Template } from '@/types';
import { collection, getDocs, query, where, orderBy, limit } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { useAuth } from '@/contexts/AuthContext';
import { createContactMessage, toggleFavorite } from '@/lib/firebaseServices';
import { toast } from 'sonner';
import { TemplateImageCarousel } from '@/components/TemplateImageCarousel';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';

// Fallback data if Firebase fails
const fallbackTemplates: Template[] = [
  {
    id: '1',
    title: 'Modern Dashboard',
    description: 'Clean and modern dashboard template with dark mode support',
    category: 'Dashboard',
    price: 49,
    imageUrl: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop&crop=center',
    rating: 4.9,
    downloads: 1234,
    featured: true,
    tags: ['React', 'TypeScript', 'Tailwind'],
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 'admin'
  },
  {
    id: '2',
    title: 'E-commerce Store',
    description: 'Complete e-commerce solution with shopping cart and checkout',
    category: 'E-commerce',
    price: 79,
    imageUrl: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=300&fit=crop&crop=center',
    rating: 4.8,
    downloads: 856,
    featured: true,
    tags: ['Next.js', 'Stripe', 'Responsive'],
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 'admin'
  },
  {
    id: '3',
    title: 'Landing Page Pro',
    description: 'High-converting landing page template for SaaS products',
    category: 'Landing Page',
    price: 39,
    imageUrl: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop&crop=center',
    rating: 4.9,
    downloads: 2341,
    featured: true,
    tags: ['HTML', 'CSS', 'JavaScript'],
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 'admin'
  },
  {
    id: '4',
    title: 'Portfolio Showcase',
    description: 'Creative portfolio template for designers and developers',
    category: 'Portfolio',
    price: 29,
    imageUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop&crop=center',
    rating: 4.7,
    downloads: 1567,
    featured: true,
    tags: ['Vue.js', 'GSAP', 'Responsive'],
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 'admin'
  }
];

export const FeaturedTemplates = () => {
  const [templates, setTemplates] = useState<Template[]>([]);
  const [loading, setLoading] = useState(true);
  const [userFavorites, setUserFavorites] = useState<string[]>([]);
  const { user, userData, refreshUserData } = useAuth();
  const router = useRouter();

  useEffect(() => {
    const fetchFeaturedTemplates = async () => {
      try {
        // First try to get templates marked as featured
        let featuredTemplates: Template[] = [];

        try {
          const featuredQuery = query(
            collection(db, 'templates'),
            where('featured', '==', true),
            orderBy('downloads', 'desc'),
            limit(20) // Increased limit to show more featured templates
          );

          const featuredSnapshot = await getDocs(featuredQuery);
          featuredTemplates = featuredSnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          })) as Template[];

          console.log('Fetched featured templates:', featuredTemplates);
        } catch (indexError) {
          console.log('Featured templates orderBy failed, trying without orderBy');
          // Fallback: fetch featured templates without orderBy
          const featuredQuery = query(
            collection(db, 'templates'),
            where('featured', '==', true)
          );

          const featuredSnapshot = await getDocs(featuredQuery);
          featuredTemplates = featuredSnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          })) as Template[];

          // Sort manually by downloads
          featuredTemplates.sort((a, b) => (b.downloads || 0) - (a.downloads || 0));
          console.log('Fetched featured templates (fallback):', featuredTemplates);
        }

        // If we have featured templates, use them
        if (featuredTemplates.length > 0) {
          // Show all featured templates for both mobile and desktop
          setTemplates(featuredTemplates);
        } else {
          // Fallback: Get all templates and organize by category
          const allQuery = query(collection(db, 'templates'));
          const allSnapshot = await getDocs(allQuery);
          const allTemplates = allSnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          })) as Template[];

          // Group by category and get one from each
          const templatesByCategory = new Map<string, Template[]>();
          allTemplates.forEach(template => {
            const category = template.category || 'Other';
            if (!templatesByCategory.has(category)) {
              templatesByCategory.set(category, []);
            }
            templatesByCategory.get(category)!.push(template);
          });

          const categoryTemplates: Template[] = [];
          templatesByCategory.forEach((templates) => {
            const sortedTemplates = templates.sort((a, b) => (b.downloads || 0) - (a.downloads || 0));
            categoryTemplates.push(sortedTemplates[0]);
          });

          const finalTemplates = categoryTemplates
            .sort((a, b) => (b.downloads || 0) - (a.downloads || 0))
            .slice(0, window.innerWidth < 768 ? 6 : 8);

          setTemplates(finalTemplates.length > 0 ? finalTemplates : fallbackTemplates);
        }
      } catch (error) {
        console.error('Error fetching featured templates:', error);
        setTemplates(fallbackTemplates);
      } finally {
        setLoading(false);
      }
    };

    fetchFeaturedTemplates();

    // Add resize listener to handle mobile/desktop switch
    const handleResize = () => {
      fetchFeaturedTemplates();
    };

    // Add interval to refresh featured templates every 30 seconds
    const refreshInterval = setInterval(() => {
      fetchFeaturedTemplates();
    }, 30000);

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
      clearInterval(refreshInterval);
    };
  }, []);

  // Update local favorites when userData changes
  useEffect(() => {
    if (userData?.favoriteTemplates) {
      setUserFavorites(userData.favoriteTemplates);
    } else {
      setUserFavorites([]);
    }
  }, [userData]);

  const handleContactRequest = async (template: Template) => {
    if (!user) {
      toast.error('Please sign in to contact us')
      router.push('/auth')
      return
    }

    // Check if user has completed profile (mobile number required)
    if (!userData?.phoneNumber || !userData?.fullName) {
      toast.error('Please complete your profile with mobile number before contacting us')
      router.push('/profile')
      return
    }

    try {
      await createContactMessage({
        userId: user.uid,
        userEmail: user.email!,
        userName: userData.fullName,
        userPhone: `${userData.countryCode || '+1'} ${userData.phoneNumber}`,
        subject: `Inquiry about ${template.title}`,
        message: `Hi, I'm interested in the ${template.title} template. Could you please provide more information about customization options and pricing?`,
        type: 'contact',
        templateId: template.id,
        templateTitle: template.title,
        status: 'pending'
      })

      toast.success('Contact request sent! We\'ll get back to you soon.')
    } catch (error) {
      console.error('Error sending contact request:', error)
      toast.error('Failed to send contact request. Please try again.')
    }
  }

  const handleBuyRequest = async (template: Template) => {
    if (!user) {
      toast.error('Please sign in to make a purchase request')
      router.push('/auth')
      return
    }

    // Check if user has completed profile (mobile number required)
    if (!userData?.phoneNumber || !userData?.fullName) {
      toast.error('Please complete your profile with mobile number before making a purchase request')
      router.push('/profile')
      return
    }

    try {
      await createContactMessage({
        userId: user.uid,
        userEmail: user.email!,
        userName: userData.fullName,
        userPhone: `${userData.countryCode || '+1'} ${userData.phoneNumber}`,
        subject: `Purchase Request for ${template.title}`,
        message: `Hi, I would like to purchase the ${template.title} template. Please provide payment instructions and delivery details.`,
        type: 'purchase-request',
        templateId: template.id,
        templateTitle: template.title,
        status: 'pending'
      })

      toast.success('Purchase request sent! We\'ll contact you with payment details.')
    } catch (error) {
      console.error('Error sending buy request:', error)
      toast.error('Failed to send purchase request. Please try again.')
    }
  }

  const handleFavoriteClick = async (e: React.MouseEvent, template: Template) => {
    e.stopPropagation();

    if (!user) {
      toast.error('Please sign in to add favorites')
      return
    }

    try {
      const isAdded = await toggleFavorite(user.uid, template.id);

      // Update local state immediately for instant UI feedback
      if (isAdded) {
        setUserFavorites(prev => [...prev, template.id]);
        toast.success('Added to favorites!')
      } else {
        setUserFavorites(prev => prev.filter(id => id !== template.id));
        toast.success('Removed from favorites!')
      }

      // Refresh user data in background
      refreshUserData();
    } catch (error) {
      console.error('Error toggling favorite:', error)
      toast.error('Failed to update favorites. Please try again.')
    }
  }

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Featured Templates
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Hand-picked premium templates for your next project
          </p>
        </div>

        {/* Templates Grid */}
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            {[1, 2, 3, 4].map((i) => (
              <Card key={i} className="animate-pulse">
                <div className="h-48 bg-gray-200 rounded-t-lg"></div>
                <CardContent className="p-6">
                  <div className="h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-6 bg-gray-200 rounded mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded mb-4"></div>
                  <div className="h-4 bg-gray-200 rounded"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <>
            {/* Mobile Swiper */}
            <div className="block md:hidden mb-12">
              <Swiper
                modules={[Navigation, Pagination, Autoplay]}
                spaceBetween={16}
                slidesPerView={1}
                navigation={false}
                pagination={{ clickable: true }}
                autoplay={{ delay: 3000, disableOnInteraction: false }}
                className="featured-templates-swiper"
              >
                {templates.map((template) => (
                  <SwiperSlide key={template.id}>
                    <Card className="group overflow-hidden hover:shadow-2xl transition-all duration-500 border-0 shadow-lg cursor-pointer bg-white rounded-xl mx-2">
                      {/* Image Container with Carousel */}
                      <div className="aspect-[4/3] bg-gradient-to-br from-blue-50 to-purple-50 relative overflow-hidden rounded-t-xl">
                        <TemplateImageCarousel
                          images={template.imageUrls && template.imageUrls.length > 0 ? template.imageUrls : [template.imageUrl]}
                          title={template.title}
                        />

                        {/* Category Badge */}
                        <div className="absolute top-3 left-3 z-10">
                          <Badge className="bg-blue-600 text-white border-0 text-xs font-medium px-2 py-1">
                            {template.category}
                          </Badge>
                        </div>

                        {/* Heart Icon */}
                        <div className="absolute top-3 right-3 z-10">
                          <Button
                            size="sm"
                            variant="secondary"
                            className="w-8 h-8 p-0 bg-white/90 hover:bg-white border-0 shadow-md cursor-pointer rounded-full"
                            onClick={(e) => handleFavoriteClick(e, template)}
                          >
                            <Heart
                              className={`h-4 w-4 ${
                                userFavorites.includes(template.id)
                                  ? 'fill-red-500 text-red-500'
                                  : 'text-gray-600'
                              }`}
                            />
                          </Button>
                        </div>
                      </div>

                      <CardContent className="p-4">
                        <div className="mb-3">
                          <h3 className="text-lg font-bold text-gray-900 group-hover:text-blue-600 transition-colors line-clamp-1 mb-1">
                            {template.title}
                          </h3>
                          <div className="flex items-center text-yellow-500 mb-2">
                            <Star className="h-4 w-4 fill-current" />
                            <span className="text-sm font-medium text-gray-700 ml-1">
                              {template.rating || 4.5}
                            </span>
                          </div>
                        </div>

                        <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                          {template.description}
                        </p>

                        {/* Tags */}
                        <div className="flex flex-wrap gap-1 mb-4">
                          <Badge variant="outline" className="text-xs px-2 py-1 bg-blue-50 text-blue-700 border-blue-200">Responsive</Badge>
                          <Badge variant="outline" className="text-xs px-2 py-1 bg-green-50 text-green-700 border-green-200">Modern</Badge>
                          <Badge variant="outline" className="text-xs px-2 py-1 bg-purple-50 text-purple-700 border-purple-200">Fast</Badge>
                        </div>

                        <div className="flex flex-col gap-2">
                          <Button
                            size="sm"
                            className="w-full cursor-pointer bg-blue-600 hover:bg-blue-700 text-white border-0 text-sm font-medium"
                            onClick={() => handleContactRequest(template)}
                          >
                            <MessageCircle className="h-4 w-4 mr-2" />
                            Contact to Buy
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            className="w-full cursor-pointer hover:bg-gray-50 border-gray-300 text-gray-700 text-sm font-medium"
                            onClick={() => {
                              if (template.previewUrl) {
                                window.open(template.previewUrl, '_blank');
                              } else {
                                toast.info('Preview not available for this template');
                              }
                            }}
                          >
                            <Eye className="h-4 w-4 mr-2" />
                            Preview
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  </SwiperSlide>
                ))}
              </Swiper>
            </div>

            {/* Desktop Grid */}
            <div className="hidden md:grid grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6 mb-12">
            {templates.map((template) => (
              <Card key={template.id} className="group overflow-hidden hover:shadow-2xl transition-all duration-500 border-0 shadow-lg cursor-pointer bg-white rounded-xl">
                {/* Image Container with Carousel */}
                <div className="aspect-[4/3] bg-gradient-to-br from-blue-50 to-purple-50 relative overflow-hidden rounded-t-xl">
                  <TemplateImageCarousel
                    images={template.imageUrls && template.imageUrls.length > 0 ? template.imageUrls : [template.imageUrl]}
                    title={template.title}
                  />

                  {/* Category Badge */}
                  <div className="absolute top-3 left-3 z-10">
                    <Badge className="bg-blue-600 text-white border-0 text-xs font-medium px-2 py-1">
                      {template.category}
                    </Badge>
                  </div>

                  {/* Heart Icon */}
                  <div className="absolute top-3 right-3 z-10">
                    <Button
                      size="sm"
                      variant="secondary"
                      className="w-8 h-8 p-0 bg-white/90 hover:bg-white border-0 shadow-md cursor-pointer rounded-full"
                      onClick={(e) => handleFavoriteClick(e, template)}
                    >
                      <Heart
                        className={`h-4 w-4 ${
                          userFavorites.includes(template.id)
                            ? 'fill-red-500 text-red-500'
                            : 'text-gray-600'
                        }`}
                      />
                    </Button>
                  </div>
                </div>

                <CardContent className="p-4">
                  <div className="mb-3">
                    <h3 className="text-lg font-bold text-gray-900 group-hover:text-blue-600 transition-colors line-clamp-1 mb-1">
                      {template.title}
                    </h3>
                    <div className="flex items-center text-yellow-500 mb-2">
                      <Star className="h-4 w-4 fill-current" />
                      <span className="text-sm font-medium text-gray-700 ml-1">
                        {template.rating || 4.5}
                      </span>
                    </div>
                  </div>

                  <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                    {template.description}
                  </p>

                  {/* Tags */}
                  <div className="flex flex-wrap gap-1 mb-4">
                    <Badge variant="outline" className="text-xs px-2 py-1 bg-blue-50 text-blue-700 border-blue-200">Responsive</Badge>
                    <Badge variant="outline" className="text-xs px-2 py-1 bg-green-50 text-green-700 border-green-200">Modern</Badge>
                    <Badge variant="outline" className="text-xs px-2 py-1 bg-purple-50 text-purple-700 border-purple-200">Fast</Badge>
                  </div>

                  <div className="flex flex-col gap-2">
                    <Button
                      size="sm"
                      className="w-full cursor-pointer bg-blue-600 hover:bg-blue-700 text-white border-0 text-sm font-medium"
                      onClick={() => handleContactRequest(template)}
                    >
                      <MessageCircle className="h-4 w-4 mr-2" />
                      Contact to Buy
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      className="w-full cursor-pointer hover:bg-gray-50 border-gray-300 text-gray-700 text-sm font-medium"
                      onClick={() => {
                        if (template.previewUrl) {
                          window.open(template.previewUrl, '_blank');
                        } else {
                          toast.info('Preview not available for this template');
                        }
                      }}
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      Preview
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
          </>
        )}

        {/* View All Button */}
        <div className="text-center">
          <Button asChild size="lg" variant="outline">
            <Link href="/templates">
              View All Templates
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
};
