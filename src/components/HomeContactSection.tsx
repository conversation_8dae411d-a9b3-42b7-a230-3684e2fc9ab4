'use client';

import React from 'react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Mail, 
  Phone, 
  MessageCircle, 
  ArrowRight,
  MapPin,
  Clock
} from 'lucide-react';

const contactMethods = [
  {
    icon: Mail,
    title: 'Email Us',
    description: 'Get in touch via email',
    contact: '<EMAIL>',
    action: 'Send Email',
    href: 'mailto:<EMAIL>',
    color: 'text-blue-600 bg-blue-100'
  },
  {
    icon: Phone,
    title: 'Call Us',
    description: 'Speak with our team',
    contact: '+****************',
    action: 'Call Now',
    href: 'tel:+15551234567',
    color: 'text-green-600 bg-green-100'
  },
  {
    icon: MessageCircle,
    title: 'Live Chat',
    description: 'Chat with support',
    contact: 'Coming Soon',
    action: 'Coming Soon',
    href: '#',
    color: 'text-purple-600 bg-purple-100'
  }
];

export const HomeContactSection = () => {
  return (
    <section className="py-16 sm:py-20 lg:py-24 bg-gradient-to-br from-gray-50 via-white to-gray-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16 lg:mb-20">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full mb-6">
            <MessageCircle className="h-8 w-8 text-white" />
          </div>
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Let's Start a Conversation
          </h2>
          <p className="text-lg sm:text-xl lg:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            Ready to bring your vision to life? Our expert team is here to help you find the perfect template
            or create something completely custom just for you.
          </p>
        </div>

        {/* Contact Methods Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 lg:gap-10 mb-16 lg:mb-20">
          {contactMethods.map((method, index) => {
            const IconComponent = method.icon;
            return (
              <Card key={index} className="group hover:shadow-2xl hover:-translate-y-2 transition-all duration-500 border-0 shadow-lg bg-white/80 backdrop-blur-sm">
                <CardContent className="p-8 lg:p-10 text-center">
                  <div className={`w-20 h-20 lg:w-24 lg:h-24 rounded-2xl ${method.color} flex items-center justify-center mx-auto mb-6 lg:mb-8 group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 shadow-lg`}>
                    <IconComponent className="h-10 w-10 lg:h-12 lg:w-12" />
                  </div>
                  <h3 className="text-xl lg:text-2xl font-bold text-gray-900 mb-3 lg:mb-4">
                    {method.title}
                  </h3>
                  <p className="text-base lg:text-lg text-gray-600 mb-4 lg:mb-5 leading-relaxed">
                    {method.description}
                  </p>
                  <p className="text-base lg:text-lg font-semibold text-gray-900 mb-6 lg:mb-8 bg-gray-50 py-2 px-4 rounded-lg">
                    {method.contact}
                  </p>
                  <Button asChild variant="default" size="lg" className="w-full group-hover:bg-gray-900 transition-colors duration-300">
                    <Link href={method.href}>
                      {method.action}
                      <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Business Hours & Location */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8 mb-12 lg:mb-16">
          {/* Business Hours */}
          <Card>
            <CardContent className="p-6 lg:p-8">
              <div className="flex items-start space-x-4">
                <div className="p-3 bg-purple-100 rounded-lg flex-shrink-0">
                  <Clock className="h-6 w-6 text-purple-600" />
                </div>
                <div className="flex-1">
                  <h3 className="text-lg lg:text-xl font-semibold text-gray-900 mb-4">Business Hours</h3>
                  <div className="space-y-2 text-sm lg:text-base">
                    <div className="flex flex-col sm:flex-row sm:justify-between">
                      <span className="text-gray-600">Monday - Friday</span>
                      <span className="font-medium text-gray-900">9:00 AM - 6:00 PM EST</span>
                    </div>
                    <div className="flex flex-col sm:flex-row sm:justify-between">
                      <span className="text-gray-600">Saturday</span>
                      <span className="font-medium text-gray-900">10:00 AM - 4:00 PM EST</span>
                    </div>
                    <div className="flex flex-col sm:flex-row sm:justify-between">
                      <span className="text-gray-600">Sunday</span>
                      <span className="font-medium text-red-600">Closed</span>
                    </div>
                  </div>
                  <p className="text-xs lg:text-sm text-blue-600 mt-4 font-medium">
                    📧 Email support available 24/7
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Office Location */}
          <Card>
            <CardContent className="p-6 lg:p-8">
              <div className="flex items-start space-x-4">
                <div className="p-3 bg-green-100 rounded-lg flex-shrink-0">
                  <MapPin className="h-6 w-6 text-green-600" />
                </div>
                <div className="flex-1">
                  <h3 className="text-lg lg:text-xl font-semibold text-gray-900 mb-4">Office Location</h3>
                  <p className="text-sm lg:text-base text-gray-600 mb-4">
                    123 Design Street<br />
                    Creative District<br />
                    San Francisco, CA 94102<br />
                    United States
                  </p>
                  <Button asChild variant="outline" size="sm">
                    <Link href="https://maps.google.com" target="_blank">
                      Get Directions
                      <ArrowRight className="ml-2 h-3 w-3" />
                    </Link>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Enhanced CTA Section */}
        <div className="text-center bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 rounded-3xl p-8 lg:p-12 shadow-2xl">
          <h3 className="text-2xl lg:text-3xl font-bold text-white mb-4">
            Ready to Get Started?
          </h3>
          <p className="text-lg text-gray-300 mb-8 max-w-2xl mx-auto">
            Join thousands of satisfied customers who have transformed their ideas into stunning digital experiences.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center max-w-lg mx-auto">
            <Button asChild size="lg" className="flex-1 bg-white text-gray-900 hover:bg-gray-100 font-semibold">
              <Link href="/contact">
                Start Your Project
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="flex-1 border-white text-white hover:bg-white hover:text-gray-900 font-semibold">
              <Link href="/templates">
                Browse Templates
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};
