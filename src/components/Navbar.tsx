'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { Menu, User, LogOut, Settings, ShoppingBag, Plus, Home, FileText, Palette, Phone, Heart, Info, Briefcase } from 'lucide-react';

export const Navbar = () => {
  const { user, userData, logout } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const pathname = usePathname();

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  const NavLinks = () => (
    <>
      <Link href="/" className={`text-sm font-medium transition-colors flex items-center cursor-pointer px-3 py-2 rounded-md ${
        pathname === '/' ? 'text-blue-600 bg-blue-50' : 'text-gray-700 hover:text-blue-600 hover:bg-blue-50'
      }`}>
        <Home className="w-4 h-4 mr-2" />
        Home
      </Link>
      <Link href="/templates" className={`text-sm font-medium transition-colors flex items-center cursor-pointer px-3 py-2 rounded-md ${
        pathname === '/templates' ? 'text-blue-600 bg-blue-50' : 'text-gray-700 hover:text-blue-600 hover:bg-blue-50'
      }`}>
        <FileText className="w-4 h-4 mr-2" />
        Templates
      </Link>
      <Link href="/work" className={`text-sm font-medium transition-colors flex items-center cursor-pointer px-3 py-2 rounded-md ${
        pathname === '/work' ? 'text-blue-600 bg-blue-50' : 'text-gray-700 hover:text-blue-600 hover:bg-blue-50'
      }`}>
        <Briefcase className="w-4 h-4 mr-2" />
        Work
      </Link>
      <Link href="/custom-request" className={`text-sm font-medium transition-colors flex items-center cursor-pointer px-3 py-2 rounded-md ${
        pathname === '/custom-request' ? 'text-blue-600 bg-blue-50' : 'text-gray-700 hover:text-blue-600 hover:bg-blue-50'
      }`}>
        <Palette className="w-4 h-4 mr-2" />
        Customize
      </Link>
      <Link href="/contact" className={`text-sm font-medium transition-colors flex items-center cursor-pointer px-3 py-2 rounded-md ${
        pathname === '/contact' ? 'text-blue-600 bg-blue-50' : 'text-gray-700 hover:text-blue-600 hover:bg-blue-50'
      }`}>
        <Phone className="w-4 h-4 mr-2" />
        Contact
      </Link>
      <Link href="/about" className={`text-sm font-medium transition-colors flex items-center cursor-pointer px-3 py-2 rounded-md ${
        pathname === '/about' ? 'text-blue-600 bg-blue-50' : 'text-gray-700 hover:text-blue-600 hover:bg-blue-50'
      }`}>
        <Info className="w-4 h-4 mr-2" />
        About
      </Link>
    </>
  );

  return (
    <nav className="border-b border-white/20 bg-white/10 backdrop-blur-md shadow-sm sticky top-0 z-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex h-14 sm:h-16 items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center">
            <span className="font-bold text-lg sm:text-xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              KaleidoNex
            </span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-6 xl:space-x-8">
            <NavLinks />
          </div>

          {/* User Actions */}
          <div className="flex items-center space-x-4">
            {user ? (
              <>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                      <Avatar className="h-8 w-8">
                        <AvatarFallback>
                          {userData?.displayName?.[0] || user.email?.[0]?.toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-56" align="end" forceMount>
                    <DropdownMenuItem asChild>
                      <Link href={userData?.role === 'admin' ? '/admin' : '/favorites'} className="flex items-center">
                        {userData?.role === 'admin' ? (
                          <Settings className="mr-2 h-4 w-4" />
                        ) : (
                          <Heart className="mr-2 h-4 w-4" />
                        )}
                        {userData?.role === 'admin' ? 'Admin Dashboard' : 'My Favorites'}
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/profile" className="flex items-center">
                        <Settings className="mr-2 h-4 w-4" />
                        Profile Settings
                      </Link>
                    </DropdownMenuItem>
                    {userData?.role !== 'admin' && (
                      <DropdownMenuItem asChild>
                        <Link href="/orders" className="flex items-center">
                          <ShoppingBag className="mr-2 h-4 w-4" />
                          My Orders
                        </Link>
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={handleLogout}>
                      <LogOut className="mr-2 h-4 w-4" />
                      Log out
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </>
            ) : (
              <div className="hidden lg:flex items-center space-x-2">
                <Button asChild variant="ghost" className="text-gray-700 hover:text-gray-900 text-sm">
                  <Link href="/auth" className="flex items-center">
                    <User className="w-4 h-4 mr-2" />
                    Sign In
                  </Link>
                </Button>
              </div>
            )}

            {/* Mobile Menu */}
            <Sheet open={isOpen} onOpenChange={setIsOpen}>
              <SheetTrigger asChild className="lg:hidden">
                <Button variant="ghost" size="sm" className="p-2">
                  <Menu className="h-5 w-5" />
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-[280px] sm:w-[350px]">
                <div className="flex flex-col space-y-4 mt-6 ml-4">
                  <div className="flex flex-col space-y-3">
                    <Link
                      href="/"
                      className={`text-sm font-medium transition-colors flex items-center py-2 px-3 rounded-md cursor-pointer ${
                        pathname === '/' ? 'text-blue-600 bg-blue-50 border-l-4 border-blue-600' : 'text-gray-700 hover:text-gray-600 hover:bg-gray-50'
                      }`}
                      onClick={() => setIsOpen(false)}
                    >
                      <Home className="w-4 h-4 mr-3" />
                      Home
                    </Link>
                    <Link
                      href="/templates"
                      className={`text-sm font-medium transition-colors flex items-center py-2 px-3 rounded-md cursor-pointer ${
                        pathname === '/templates' ? 'text-blue-600 bg-blue-50 border-l-4 border-blue-600' : 'text-gray-700 hover:text-gray-600 hover:bg-gray-50'
                      }`}
                      onClick={() => setIsOpen(false)}
                    >
                      <FileText className="w-4 h-4 mr-3" />
                      Templates
                    </Link>
                    <Link
                      href="/work"
                      className={`text-sm font-medium transition-colors flex items-center py-2 px-3 rounded-md cursor-pointer ${
                        pathname === '/work' ? 'text-blue-600 bg-blue-50 border-l-4 border-blue-600' : 'text-gray-700 hover:text-gray-600 hover:bg-gray-50'
                      }`}
                      onClick={() => setIsOpen(false)}
                    >
                      <Briefcase className="w-4 h-4 mr-3" />
                      Work
                    </Link>
                    <Link
                      href="/custom-request"
                      className={`text-sm font-medium transition-colors flex items-center py-2 px-3 rounded-md cursor-pointer ${
                        pathname === '/custom-request' ? 'text-blue-600 bg-blue-50 border-l-4 border-blue-600' : 'text-gray-700 hover:text-gray-600 hover:bg-gray-50'
                      }`}
                      onClick={() => setIsOpen(false)}
                    >
                      <Palette className="w-4 h-4 mr-3" />
                      Customize
                    </Link>
                    <Link
                      href="/contact"
                      className={`text-sm font-medium transition-colors flex items-center py-2 px-3 rounded-md cursor-pointer ${
                        pathname === '/contact' ? 'text-blue-600 bg-blue-50 border-l-4 border-blue-600' : 'text-gray-700 hover:text-gray-600 hover:bg-gray-50'
                      }`}
                      onClick={() => setIsOpen(false)}
                    >
                      <Phone className="w-4 h-4 mr-3" />
                      Contact
                    </Link>
                    <Link
                      href="/about"
                      className={`text-sm font-medium transition-colors flex items-center py-2 px-3 rounded-md cursor-pointer ${
                        pathname === '/about' ? 'text-blue-600 bg-blue-50 border-l-4 border-blue-600' : 'text-gray-700 hover:text-gray-600 hover:bg-gray-50'
                      }`}
                      onClick={() => setIsOpen(false)}
                    >
                      <Info className="w-4 h-4 mr-3" />
                      About
                    </Link>
                  </div>

                  {user ? (
                    <div className="flex flex-col space-y-3 pt-4 border-t">
                      <button
                        onClick={() => {
                          logout();
                          setIsOpen(false);
                        }}
                        className="text-left text-sm font-medium text-red-600 hover:text-red-700 transition-colors flex items-center py-2 cursor-pointer"
                      >
                        <LogOut className="w-4 h-4 mr-3" />
                        Sign Out
                      </button>
                    </div>
                  ) : (
                    <div className="flex flex-col space-y-3 pt-4 border-t">
                      <Link
                        href="/auth"
                        className="text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors flex items-center py-2"
                        onClick={() => setIsOpen(false)}
                      >
                        <User className="w-4 h-4 mr-3" />
                        Sign In
                      </Link>
                      <Link
                        href="/auth?mode=signup"
                        className="text-sm font-medium text-blue-600 hover:text-blue-700 transition-colors flex items-center py-2"
                        onClick={() => setIsOpen(false)}
                      >
                        <Plus className="w-4 h-4 mr-3" />
                        Get Started
                      </Link>
                    </div>
                  )}
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </nav>
  );
};
