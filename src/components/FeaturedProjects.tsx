'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ExternalLink, Calendar, ArrowRight, Eye } from 'lucide-react';
import Image from 'next/image';
import { collection, getDocs, query, where, orderBy } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Project } from '@/types/project';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';

export const FeaturedProjects = () => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchFeaturedProjects();
  }, []);

  const fetchFeaturedProjects = async () => {
    try {
      setLoading(true);
      console.log('Fetching featured projects from Firebase...');

      // Try with orderBy first
      let projectsData: Project[] = [];
      try {
        const projectsQuery = query(
          collection(db, 'projects'),
          where('status', '==', 'active'),
          where('featured', '==', true),
          orderBy('order', 'asc')
        );
        const querySnapshot = await getDocs(projectsQuery);
        projectsData = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate() || new Date(),
          updatedAt: doc.data().updatedAt?.toDate() || new Date(),
        })) as Project[];
      } catch (indexError) {
        console.log('OrderBy failed, trying without orderBy');
        // Fallback: fetch without orderBy
        const projectsQuery = query(
          collection(db, 'projects'),
          where('status', '==', 'active'),
          where('featured', '==', true)
        );
        const querySnapshot = await getDocs(projectsQuery);
        projectsData = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate() || new Date(),
          updatedAt: doc.data().updatedAt?.toDate() || new Date(),
        })) as Project[];

        // Sort manually
        projectsData.sort((a, b) => (a.order || 0) - (b.order || 0));
      }

      // If no featured projects found, get first 3 active projects
      if (projectsData.length === 0) {
        console.log('No featured projects found, fetching first 3 active projects');
        try {
          const fallbackQuery = query(
            collection(db, 'projects'),
            where('status', '==', 'active'),
            orderBy('order', 'asc')
          );
          const querySnapshot = await getDocs(fallbackQuery);
          projectsData = querySnapshot.docs.slice(0, 3).map(doc => ({
            id: doc.id,
            ...doc.data(),
            createdAt: doc.data().createdAt?.toDate() || new Date(),
            updatedAt: doc.data().updatedAt?.toDate() || new Date(),
          })) as Project[];
        } catch (fallbackError) {
          console.log('Fallback query failed, using basic query');
          const basicQuery = query(collection(db, 'projects'));
          const querySnapshot = await getDocs(basicQuery);
          projectsData = querySnapshot.docs
            .filter(doc => doc.data().status === 'active')
            .slice(0, 3)
            .map(doc => ({
              id: doc.id,
              ...doc.data(),
              createdAt: doc.data().createdAt?.toDate() || new Date(),
              updatedAt: doc.data().updatedAt?.toDate() || new Date(),
            })) as Project[];
        }
      }

      setProjects(projectsData);
      console.log('Fetched featured projects:', projectsData.length);
    } catch (error) {
      console.error('Error fetching featured projects:', error);
      // Don't use fallback data - show empty if no real data
      setProjects([]);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading featured projects...</p>
          </div>
        </div>
      </section>
    );
  }

  if (projects.length === 0) {
    return null;
  }

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Featured Projects
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto mb-8">
            A glimpse into the <span className="text-blue-600 font-semibold">websites</span> that we have built.
          </p>
        </div>

        {/* Projects Grid */}
        {/* Mobile Swiper */}
        <div className="block md:hidden mb-12">
          <Swiper
            modules={[Navigation, Pagination, Autoplay]}
            spaceBetween={16}
            slidesPerView={1}
            navigation={false}
            pagination={{ clickable: true }}
            autoplay={{ delay: 4000, disableOnInteraction: false }}
            className="featured-projects-swiper"
          >
            {projects.map((project) => (
              <SwiperSlide key={project.id}>
                <Card className="group overflow-hidden hover:shadow-2xl transition-all duration-500 border-0 shadow-lg cursor-pointer bg-white rounded-xl mx-2">
                  {/* Image Container */}
                  <div className="aspect-video bg-gradient-to-br from-blue-50 to-purple-50 relative overflow-hidden">
                    <Image
                      src={project.images[0] || 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=600&fit=crop'}
                      alt={project.title}
                      width={400}
                      height={300}
                      className="w-full h-full object-contain bg-gray-50 group-hover:scale-105 transition-transform duration-500"
                      unoptimized
                    />

                    {/* Category Badge */}
                    <div className="absolute top-3 left-3">
                      <Badge className="bg-blue-600 text-white border-0 text-xs font-medium">
                        {project.category}
                      </Badge>
                    </div>

                    {/* Hover Overlay */}
                    <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                      <Button
                        size="sm"
                        variant="secondary"
                        className="bg-white/95 hover:bg-white text-black border-0 text-sm font-medium shadow-lg cursor-pointer"
                        onClick={() => project.livePreviewUrl && window.open(project.livePreviewUrl, '_blank')}
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        View Project
                      </Button>
                    </div>
                  </div>

                  <CardContent className="p-6">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center text-xs text-gray-500">
                        <Calendar className="w-3 h-3 mr-1" />
                        {new Date(project.createdAt).toLocaleDateString()}
                      </div>
                    </div>

                    <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                      {project.title}
                    </h3>

                    <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                      {project.description}
                    </p>

                    <div className="flex flex-wrap gap-2 mb-4">
                      {project.technologies.slice(0, 3).map((tech, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {tech}
                        </Badge>
                      ))}
                      {project.technologies.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{project.technologies.length - 3} more
                        </Badge>
                      )}
                    </div>

                    {project.livePreviewUrl && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full cursor-pointer hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700"
                        onClick={() => window.open(project.livePreviewUrl, '_blank')}
                      >
                        <ExternalLink className="w-4 h-4 mr-2" />
                        View Live Project
                      </Button>
                    )}
                  </CardContent>
                </Card>
              </SwiperSlide>
            ))}
          </Swiper>
        </div>

        {/* Desktop Grid */}
        <div className="hidden md:grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          {projects.map((project) => (
            <Card key={project.id} className="group overflow-hidden hover:shadow-2xl transition-all duration-500 border-0 shadow-lg cursor-pointer bg-white rounded-xl">
              {/* Image Container */}
              <div className="aspect-video bg-gradient-to-br from-blue-50 to-purple-50 relative overflow-hidden">
                <Image
                  src={project.images[0] || 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=600&fit=crop'}
                  alt={project.title}
                  width={400}
                  height={300}
                  className="w-full h-full object-contain bg-gray-50 group-hover:scale-105 transition-transform duration-500"
                  unoptimized
                />

                {/* Category Badge */}
                <div className="absolute top-3 left-3">
                  <Badge className="bg-blue-600 text-white border-0 text-xs font-medium">
                    {project.category}
                  </Badge>
                </div>

                {/* Hover Overlay */}
                <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                  <Button
                    size="sm"
                    variant="secondary"
                    className="bg-white/95 hover:bg-white text-black border-0 text-sm font-medium shadow-lg cursor-pointer"
                    onClick={() => project.livePreviewUrl && window.open(project.livePreviewUrl, '_blank')}
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    View Project
                  </Button>
                </div>
              </div>

              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center text-xs text-gray-500">
                    <Calendar className="w-3 h-3 mr-1" />
                    {new Date(project.createdAt).toLocaleDateString()}
                  </div>
                </div>

                <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                  {project.title}
                </h3>

                <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                  {project.description}
                </p>

                <div className="flex flex-wrap gap-2 mb-4">
                  {project.technologies.slice(0, 3).map((tech, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {tech}
                    </Badge>
                  ))}
                  {project.technologies.length > 3 && (
                    <Badge variant="outline" className="text-xs">
                      +{project.technologies.length - 3} more
                    </Badge>
                  )}
                </div>

                {project.livePreviewUrl && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full cursor-pointer hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700"
                    onClick={() => window.open(project.livePreviewUrl, '_blank')}
                  >
                    <ExternalLink className="w-4 h-4 mr-2" />
                    View Live Project
                  </Button>
                )}
              </CardContent>
            </Card>
          ))}
        </div>

        {/* View All Button */}
        <div className="text-center">
          <Link href="/work">
            <Button 
              size="lg" 
              className="cursor-pointer bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 font-medium shadow-lg hover:shadow-xl transition-all duration-300"
            >
              View All Projects
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
};
