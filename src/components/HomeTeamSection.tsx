'use client';

import React, { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import Image from 'next/image';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Linkedin, Twitter, Mail, ArrowRight, Users, ChevronLeft, ChevronRight } from 'lucide-react';
import Link from 'next/link';
import { collection, getDocs, query, where, orderBy } from 'firebase/firestore';
import { db } from '@/lib/firebase';

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

interface TeamMember {
  id: string;
  name: string;
  position: string;
  bio: string;
  imageUrl: string;
  email?: string;
  linkedin?: string;
  twitter?: string;
  order: number;
  active: boolean;
}

export const HomeTeamSection = () => {
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const sectionRef = useRef<HTMLDivElement>(null);
  const teamRef = useRef<HTMLDivElement>(null);
  const sliderRef = useRef<HTMLDivElement>(null);

  // Fetch team members from Firebase
  useEffect(() => {
    const fetchTeamMembers = async () => {
      try {
        // First try with orderBy, if it fails, try without
        let querySnapshot: any;
        try {
          const q = query(
            collection(db, 'teamMembers'),
            where('active', '==', true),
            orderBy('order', 'asc')
          );
          querySnapshot = await getDocs(q);
        } catch (indexError) {
          console.log('Composite index not available, fetching without orderBy');
          // Fallback: fetch active team members without orderBy
          const q = query(
            collection(db, 'teamMembers'),
            where('active', '==', true)
          );
          querySnapshot = await getDocs(q);
        }

        const members = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as TeamMember[];

        // Sort manually if needed
        const sortedMembers = members.sort((a, b) => (a.order || 0) - (b.order || 0));
        setTeamMembers(sortedMembers);
        console.log('Fetched team members:', sortedMembers);
      } catch (error) {
        console.error('Error fetching team members:', error);
        // Fallback to empty array if fetch fails
        setTeamMembers([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTeamMembers();
  }, []);

  // GSAP animations
  useEffect(() => {
    if (isLoading || teamMembers.length === 0) return;

    const ctx = gsap.context(() => {
      // Section title animation
      gsap.fromTo(sectionRef.current?.querySelector('.section-title'),
        { opacity: 0, y: 30 },
        {
          opacity: 1,
          y: 0,
          duration: 0.8,
          scrollTrigger: {
            trigger: sectionRef.current,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse"
          }
        }
      );

      // Team cards animation
      gsap.fromTo(teamRef.current?.children || [],
        { opacity: 0, y: 50, scale: 0.9 },
        {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 0.6,
          stagger: 0.1,
          scrollTrigger: {
            trigger: teamRef.current,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse"
          }
        }
      );
    });

    return () => ctx.revert();
  }, [isLoading, teamMembers]);

  // Slider functionality
  const itemsPerSlide = 4;
  const totalSlides = Math.ceil(teamMembers.length / itemsPerSlide);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % totalSlides);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + totalSlides) % totalSlides);
  };

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
  };

  // Auto-slide functionality
  useEffect(() => {
    if (teamMembers.length <= itemsPerSlide) return;

    const interval = setInterval(() => {
      nextSlide();
    }, 5000); // Change slide every 5 seconds

    return () => clearInterval(interval);
  }, [teamMembers.length, totalSlides]);

  if (isLoading) {
    return (
      <section className="py-16 sm:py-20 lg:py-24 bg-gradient-to-br from-gray-50 to-blue-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading team members...</p>
          </div>
        </div>
      </section>
    );
  }

  if (teamMembers.length === 0) {
    return null; // Don't show section if no team members
  }

  return (
    <section ref={sectionRef} className="py-16 sm:py-20 lg:py-24 bg-gradient-to-br from-slate-50 via-white to-blue-50/30 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.05),transparent_50%)] pointer-events-none" />
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(147,51,234,0.05),transparent_50%)] pointer-events-none" />

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Section Header */}
        <div className="section-title text-center mb-12 lg:mb-20">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-blue-50 border border-blue-200 rounded-full mb-6">
            <Users className="h-4 w-4 text-blue-600" />
            <span className="text-blue-700 font-medium text-sm">Our Team</span>
          </div>
          <h2 className="text-3xl sm:text-4xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight">
            Meet the People Behind
            <br />
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              KaleidoNex
            </span>
          </h2>
          <p className="text-lg sm:text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            Our diverse team of experts brings together creativity, technical excellence,
            and business acumen to deliver exceptional results that exceed expectations.
          </p>
        </div>

        {/* Team Grid */}
        <div className="relative mb-16">
          {/* Grid Container */}
          <div className="overflow-hidden" ref={sliderRef}>
            <div
              ref={teamRef}
              className="flex transition-transform duration-700 ease-out"
              style={{ transform: `translateX(-${currentSlide * 100}%)` }}
            >
              {Array.from({ length: totalSlides }).map((_, slideIndex) => (
                <div key={slideIndex} className="w-full flex-shrink-0">
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 lg:gap-8">
                    {teamMembers
                      .slice(slideIndex * itemsPerSlide, (slideIndex + 1) * itemsPerSlide)
                      .map((member) => (
                        <Card key={member.id} className="border-0 shadow-lg overflow-hidden bg-white rounded-xl">
                          <CardContent className="p-0">
                            {/* Profile Image */}
                            <div className="aspect-square relative overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100">
                              <Image
                                src={member.imageUrl}
                                alt={member.name}
                                width={400}
                                height={400}
                                className="w-full h-full object-cover"
                              />
                            </div>

                            {/* Content */}
                            <div className="p-6 text-center">
                              <h3 className="text-xl font-bold text-gray-900 mb-2">
                                {member.name}
                              </h3>
                              <p className="text-blue-600 font-medium text-sm mb-3">
                                {member.position}
                              </p>
                              <p className="text-gray-600 text-sm leading-relaxed mb-4">
                                {member.bio}
                              </p>

                              {/* Social Links */}
                              <div className="flex justify-center gap-3">
                                {member.linkedin && (
                                  <a
                                    href={member.linkedin}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="p-2 bg-gray-100 rounded-full hover:bg-blue-100 transition-colors cursor-pointer"
                                  >
                                    <Linkedin className="h-4 w-4 text-gray-600 hover:text-blue-600" />
                                  </a>
                                )}
                                {member.twitter && (
                                  <a
                                    href={member.twitter}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="p-2 bg-gray-100 rounded-full hover:bg-blue-100 transition-colors cursor-pointer"
                                  >
                                    <Twitter className="h-4 w-4 text-gray-600 hover:text-blue-600" />
                                  </a>
                                )}
                                {member.email && (
                                  <a
                                    href={`mailto:${member.email}`}
                                    className="p-2 bg-gray-100 rounded-full hover:bg-blue-100 transition-colors cursor-pointer"
                                  >
                                    <Mail className="h-4 w-4 text-gray-600 hover:text-blue-600" />
                                  </a>
                                )}
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Navigation Arrows */}
          {totalSlides > 1 && (
            <>
              <Button
                variant="secondary"
                size="sm"
                className="absolute -left-2 top-1/2 transform -translate-y-1/2 w-12 h-12 p-0 bg-white/95 hover:bg-white shadow-xl border-0 rounded-full z-10 hover:scale-110 transition-all duration-300 cursor-pointer"
                onClick={prevSlide}
              >
                <ChevronLeft className="h-6 w-6 text-gray-700" />
              </Button>
              <Button
                variant="secondary"
                size="sm"
                className="absolute -right-2 top-1/2 transform -translate-y-1/2 w-12 h-12 p-0 bg-white/95 hover:bg-white shadow-xl border-0 rounded-full z-10 hover:scale-110 transition-all duration-300 cursor-pointer"
                onClick={nextSlide}
              >
                <ChevronRight className="h-6 w-6 text-gray-700" />
              </Button>
            </>
          )}

          {/* Modern Dots Indicator */}
          {totalSlides > 1 && (
            <div className="flex justify-center mt-8 gap-3">
              {Array.from({ length: totalSlides }).map((_, index) => (
                <button
                  key={index}
                  className={`transition-all duration-500 cursor-pointer ${
                    index === currentSlide
                      ? 'w-8 h-2 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full'
                      : 'w-2 h-2 bg-gray-300 hover:bg-gray-400 rounded-full hover:scale-125'
                  }`}
                  onClick={() => goToSlide(index)}
                />
              ))}
            </div>
          )}
        </div>

        {/* Call to Action */}
        <div className="text-center mt-12">
          <div className="inline-flex flex-col sm:flex-row items-center gap-4">
            <Button asChild size="lg" className="px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer">
              <Link href="/about" className="flex items-center">
                Learn More About Us
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <p className="text-gray-500 text-sm">
              Discover our journey and values
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};
