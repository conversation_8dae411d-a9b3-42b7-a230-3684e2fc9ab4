'use client';

import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Volume2, VolumeX } from 'lucide-react';

export const HeroSection = () => {
  const [isMuted, setIsMuted] = useState(true);
  const videoRef = useRef<HTMLVideoElement>(null);

  const toggleMute = () => {
    if (videoRef.current) {
      videoRef.current.muted = !videoRef.current.muted;
      setIsMuted(videoRef.current.muted);
    }
  };

  return (
    <>
      {/* Hero Video Section - 16:9 on mobile, full screen on desktop */}
      <section className="relative w-full overflow-hidden h-screen sm:h-screen aspect-video sm:aspect-auto">
        {/* Video Container */}
        <div className="absolute inset-0">
          <video
            ref={videoRef}
            className="w-full h-full object-cover cursor-pointer"
            autoPlay
            loop
            muted={isMuted}
            playsInline
            webkit-playsinline="true"
            preload="metadata"
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
              objectPosition: 'center'
            }}
          >
            <source src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4" type="video/mp4" />
            {/* Fallback content */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-600 to-purple-600 flex items-center justify-center">
              <div className="text-center text-white">
                <div className="w-20 h-20 mx-auto mb-4 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                  <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                  </svg>
                </div>
                <p className="text-lg font-medium">Video Loading...</p>
              </div>
            </div>
          </video>

          {/* Video Overlay */}
          <div className="absolute inset-0 bg-black/30" />

          {/* Video Controls */}
          <div className="absolute bottom-6 right-6 z-10">
            <Button
              variant="secondary"
              size="sm"
              onClick={toggleMute}
              className="bg-black/50 hover:bg-black/70 text-white border-0 backdrop-blur-sm cursor-pointer"
            >
              {isMuted ? (
                <VolumeX className="h-4 w-4" />
              ) : (
                <Volume2 className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
      </section>

      {/* Content Below Hero - Only visible on mobile */}
      <section className="sm:hidden bg-white py-8 px-4">
        <div className="container mx-auto text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            Welcome to KaleidoNex
          </h1>
          <p className="text-gray-600 mb-6 leading-relaxed">
            Discover amazing templates and bring your creative vision to life with our professional designs.
          </p>
          <div className="flex flex-col gap-3">
            <Button className="w-full cursor-pointer">
              Browse Templates
            </Button>
            <Button variant="outline" className="w-full cursor-pointer">
              Get Started
            </Button>
          </div>
        </div>
      </section>
    </>
  );
};
