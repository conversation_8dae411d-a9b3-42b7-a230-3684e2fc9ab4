'use client';

import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Volume2, VolumeX } from 'lucide-react';

export const HeroSection = () => {
  const [isMuted, setIsMuted] = useState(true);
  const videoRef = useRef<HTMLVideoElement>(null);

  const toggleMute = () => {
    if (videoRef.current) {
      videoRef.current.muted = !videoRef.current.muted;
      setIsMuted(videoRef.current.muted);
    }
  };

  return (
    <>
      {/* Hero Video Section - Full screen on mobile and desktop */}
      <section className="relative w-full overflow-hidden h-screen">
        {/* Video Container */}
        <div className="absolute inset-0">
          <video
            ref={videoRef}
            className="w-full h-full object-cover cursor-pointer"
            autoPlay
            loop
            muted={isMuted}
            playsInline
            webkit-playsinline="true"
            preload="metadata"
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
              objectPosition: 'center'
            }}
          >
            <source src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4" type="video/mp4" />
            {/* Fallback content */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-600 to-purple-600 flex items-center justify-center">
              <div className="text-center text-white">
                <div className="w-20 h-20 mx-auto mb-4 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                  <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                  </svg>
                </div>
                <p className="text-lg font-medium">Video Loading...</p>
              </div>
            </div>
          </video>

          {/* Video Overlay */}
          <div className="absolute inset-0 bg-black/30" />

          {/* Content Overlay - Centered on screen */}
          <div className="absolute inset-0 flex items-center justify-center z-10">
            <div className="text-center text-white px-4 max-w-2xl mx-auto">
              <h1 className="text-3xl sm:text-4xl lg:text-6xl font-bold mb-4 sm:mb-6">
                Welcome to KaleidoNex
              </h1>
              <p className="text-lg sm:text-xl mb-6 sm:mb-8 leading-relaxed opacity-90">
                Discover amazing templates and bring your creative vision to life with our professional designs.
              </p>
              <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center">
                <Button className="px-6 py-3 sm:px-8 sm:py-4 cursor-pointer text-base sm:text-lg">
                  Browse Templates
                </Button>
                <Button variant="outline" className="px-6 py-3 sm:px-8 sm:py-4 cursor-pointer text-base sm:text-lg bg-white/10 border-white/30 text-white hover:bg-white/20">
                  Get Started
                </Button>
              </div>
            </div>
          </div>

          {/* Video Controls */}
          <div className="absolute bottom-6 right-6 z-20">
            <Button
              variant="secondary"
              size="sm"
              onClick={toggleMute}
              className="bg-black/50 hover:bg-black/70 text-white border-0 backdrop-blur-sm cursor-pointer"
            >
              {isMuted ? (
                <VolumeX className="h-4 w-4" />
              ) : (
                <Volume2 className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
      </section>
    </>
  );
};
