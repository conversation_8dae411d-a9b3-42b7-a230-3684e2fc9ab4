'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  ExternalLink, 
  Eye, 
  ArrowRight,
  Zap,
  Award,
  Users,
  TrendingUp
} from 'lucide-react';
import { collection, getDocs, query, where, orderBy } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Project } from '@/types/project';

export default function WorkPage() {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [categories, setCategories] = useState<string[]>(['All']);

  useEffect(() => {
    fetchProjects();
  }, []);

  const fetchProjects = async () => {
    try {
      console.log('Fetching projects from Firebase...');
      
      // Try with orderBy first
      let projectsData: Project[] = [];
      try {
        const projectsQuery = query(
          collection(db, 'projects'),
          where('status', '==', 'active'),
          orderBy('order', 'asc')
        );
        const querySnapshot = await getDocs(projectsQuery);
        projectsData = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate() || new Date(),
          updatedAt: doc.data().updatedAt?.toDate() || new Date(),
        })) as Project[];
      } catch (indexError) {
        console.log('OrderBy failed, trying without orderBy');
        // Fallback: fetch without orderBy
        const projectsQuery = query(
          collection(db, 'projects'),
          where('status', '==', 'active')
        );
        const querySnapshot = await getDocs(projectsQuery);
        projectsData = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate() || new Date(),
          updatedAt: doc.data().updatedAt?.toDate() || new Date(),
        })) as Project[];
        
        // Sort manually
        projectsData.sort((a, b) => (a.order || 0) - (b.order || 0));
      }

      console.log('Fetched projects:', projectsData.length);
      setProjects(projectsData);

      // Extract unique categories
      const uniqueCategories = ['All', ...new Set(projectsData.map(p => p.category))];
      setCategories(uniqueCategories);
    } catch (error) {
      console.error('Error fetching projects:', error);
      
      // Fallback to mock data
      const mockProjects: Project[] = [
        {
          id: '1',
          title: 'Cursor',
          description: 'A code editor powered by AI that helps write code faster and better. Build software faster in an IDE designed for pair-programming with AI.',
          category: 'Development Tools',
          images: [
            'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=600&h=400&fit=crop',
            'https://images.unsplash.com/photo-1517180102446-f3ece451e9d8?w=600&h=400&fit=crop'
          ],
          livePreviewUrl: 'https://cursor.sh',
          technologies: ['AI', 'Code Editor', 'TypeScript'],
          featured: true,
          order: 1,
          status: 'active',
          createdAt: new Date(),
          updatedAt: new Date(),
          createdBy: 'admin'
        },
        {
          id: '2',
          title: 'Moonbeam',
          description: 'Never Write From Scratch Again. Kickstart your next great document with Moonbeam - Your long form AI Writing Assistant.',
          category: 'AI Writing',
          images: [
            'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=600&h=400&fit=crop',
            'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=600&h=400&fit=crop'
          ],
          livePreviewUrl: 'https://moonbeam.ai',
          technologies: ['AI', 'Writing', 'Document'],
          featured: true,
          order: 2,
          status: 'active',
          createdAt: new Date(),
          updatedAt: new Date(),
          createdBy: 'admin'
        }
      ];
      
      setProjects(mockProjects);
      setCategories(['All', 'Development Tools', 'AI Writing']);
    } finally {
      setLoading(false);
    }
  };

  const filteredProjects = projects.filter(project => 
    selectedCategory === 'All' || project.category === selectedCategory
  );

  const stats = [
    { icon: Award, label: 'Projects Completed', value: '150+' },
    { icon: Users, label: 'Happy Clients', value: '500+' },
    { icon: TrendingUp, label: 'Success Rate', value: '98%' },
    { icon: Zap, label: 'Years Experience', value: '5+' }
  ];

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-20">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading our amazing work...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      {/* Hero Section */}
      <section className="py-16 sm:py-20 lg:py-24">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full mb-6">
              <Award className="h-8 w-8 text-white" />
            </div>
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
              A glimpse into our <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">projects</span>
            </h1>
            <p className="text-xl sm:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              A look at some of the amazing webapps that we've built recently.
            </p>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8 mb-16">
            {stats.map((stat, index) => {
              const IconComponent = stat.icon;
              return (
                <Card key={index} className="text-center p-6 hover:shadow-lg transition-shadow duration-300">
                  <CardContent className="p-0">
                    <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <IconComponent className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="text-2xl font-bold text-gray-900 mb-1">{stat.value}</div>
                    <div className="text-sm text-gray-600">{stat.label}</div>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* Category Filter */}
          <div className="flex flex-wrap justify-center gap-3 mb-12">
            {categories.map((category) => (
              <Button
                key={category}
                variant={selectedCategory === category ? "default" : "outline"}
                onClick={() => setSelectedCategory(category)}
                className="rounded-full"
              >
                {category}
              </Button>
            ))}
          </div>
        </div>
      </section>

      {/* Projects Grid */}
      <section className="pb-16 sm:pb-20 lg:pb-24">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12">
            {filteredProjects.map((project, index) => (
              <div key={project.id} className="group">
                <Card className="overflow-hidden hover:shadow-2xl transition-all duration-500 border-0 shadow-lg">
                  <div className="flex flex-col lg:flex-row">
                    {/* Project Info */}
                    <div className="lg:w-1/2 p-8 lg:p-10 flex flex-col justify-center">
                      <div className="mb-6">
                        <Badge variant="secondary" className="mb-4">
                          {project.category}
                        </Badge>
                        <h3 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-4">
                          {project.title}
                        </h3>
                        <p className="text-gray-600 leading-relaxed mb-6">
                          {project.description}
                        </p>
                        <div className="flex flex-wrap gap-2 mb-6">
                          {project.technologies.map((tech, techIndex) => (
                            <Badge key={techIndex} variant="outline" className="text-xs">
                              {tech}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      {project.livePreviewUrl && (
                        <Button asChild className="w-fit">
                          <a href={project.livePreviewUrl} target="_blank" rel="noopener noreferrer">
                            Live Preview
                            <ExternalLink className="ml-2 h-4 w-4" />
                          </a>
                        </Button>
                      )}
                    </div>

                    {/* Project Images */}
                    <div className="lg:w-1/2 relative">
                      <div className="aspect-[4/3] lg:aspect-auto lg:h-full relative overflow-hidden">
                        {project.images.map((image, imageIndex) => (
                          <img
                            key={imageIndex}
                            src={image}
                            alt={`${project.title} - Image ${imageIndex + 1}`}
                            className={`absolute inset-0 w-full h-full object-contain bg-gray-50 transition-opacity duration-1000 ${
                              imageIndex === 0 ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'
                            }`}
                          />
                        ))}
                        <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent" />
                      </div>
                    </div>
                  </div>
                </Card>
              </div>
            ))}
          </div>

          {filteredProjects.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <Award className="h-16 w-16 mx-auto" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">No projects found</h3>
              <p className="text-gray-600">
                {selectedCategory === 'All' 
                  ? 'No projects available at the moment.' 
                  : `No projects found in "${selectedCategory}" category.`
                }
              </p>
            </div>
          )}
        </div>
      </section>
    </div>
  );
}
