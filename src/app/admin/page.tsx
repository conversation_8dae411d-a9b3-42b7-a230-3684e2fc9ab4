'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import {
  Users,
  FileText,
  ShoppingCart,
  DollarSign,
  Settings,
  BarChart3,
  Database,
  MessageSquare,
  Palette,
  CreditCard,
  Filter,
  Bot,
  Star,
  Briefcase,
} from 'lucide-react';
import TemplatesTab from '@/components/admin/TemplatesTab';
import StatusDropdown from '@/components/admin/StatusDropdown';
import ChatbotTab from '@/components/admin/ChatbotTab';
import CareersTab from '@/components/admin/CareersTab';
import InternshipsTab from '@/components/admin/InternshipsTab';
import ReviewsTab from '@/components/admin/ReviewsTab';
import ProjectsTab from '@/components/admin/ProjectsTab';
import { TeamTab } from '@/components/admin/TeamTab';
import Link from 'next/link';
import {
  getDashboardStats,
  getCustomRequests,
  getAllUsers,
  updateCustomRequestStatus,
  subscribeToCustomRequests,
  getContactMessages,
  updateContactMessageStatus,
  subscribeToContactMessages
} from '@/lib/firebaseServices';
import { CustomRequest, User, ContactMessage } from '@/types';
import type { ContactMessage as ContactMessageType } from '@/types';

interface Timestamp {
  seconds: number;
  nanoseconds: number;
}

interface BaseItem {
  id: string;
  status: 'pending' | 'in-progress' | 'completed' | 'cancelled' | 'declined';
  createdAt: Timestamp | Date;
  updatedAt?: Timestamp | Date;
}

interface CustomRequestItem extends BaseItem {
  type: 'custom';
  title: string;
  description: string;
  userName: string;
  userPhone: string;
  userEmail: string;
}

interface ContactMessageItem extends BaseItem {
  type: 'contact';
  subject: string;
  message: string;
  email: string;
  name: string;
}

type RequestItem = CustomRequestItem | ContactMessageItem;

function isCustomRequest(obj: unknown): obj is CustomRequest {
  return obj !== null && typeof obj === 'object' && 'title' in obj && 'description' in obj;
}

function isContactMessage(obj: unknown): obj is ContactMessage {
  return obj !== null && typeof obj === 'object' && 'subject' in obj && 'message' in obj;
}

function isTimestamp(obj: unknown): obj is Timestamp {
  return obj !== null && typeof obj === 'object' && 'seconds' in obj && typeof (obj as { seconds: unknown }).seconds === 'number';
}

function getDateFromTimestamp(timestamp: Timestamp | Date): Date {
  if (isTimestamp(timestamp)) {
    return new Date(timestamp.seconds * 1000);
  }
  return timestamp;
}

interface DashboardStats {
  totalUsers: number;
  totalTemplates: number;
  totalRequests: number;
  pendingRequests: number;
  totalSales: number;
  customizations: number;
}

const AdminDashboard: React.FC = () => {
  const { user, userData } = useAuth();
  const [stats, setStats] = useState<DashboardStats>({
    totalUsers: 0,
    totalTemplates: 0,
    totalRequests: 0,
    pendingRequests: 0,
    totalSales: 0,
    customizations: 0
  });
  const [customRequests, setCustomRequests] = useState<CustomRequest[]>([]);
  const [contactMessages, setContactMessages] = useState<ContactMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('templates');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedRequest, setSelectedRequest] = useState<CustomRequest | ContactMessageType | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch dashboard stats
        const dashboardStats = await getDashboardStats();
        setStats(dashboardStats);

        // Fetch custom requests - Show all data
        const requests = await getCustomRequests();
        setCustomRequests(requests);

        // Fetch contact messages - Show all data
        const messages = await getContactMessages();
        setContactMessages(messages);

      } catch (error: unknown) {
        console.error('Error fetching admin data:', error);
        setError('Failed to load dashboard data');
      } finally {
        setLoading(false);
      }
    };

    if (user && userData?.role === 'admin') {
      fetchData();

      // Set up real-time listeners - Show all data
      const unsubscribeRequests = subscribeToCustomRequests((requests) => {
        setCustomRequests(requests);
      });

      const unsubscribeMessages = subscribeToContactMessages((messages) => {
        setContactMessages(messages);
      });

      return () => {
        unsubscribeRequests();
        unsubscribeMessages();
      };
    }
  }, [user, userData]);

  const handleUpdateRequestStatus = async (requestId: string, status: CustomRequest['status']) => {
    try {
      await updateCustomRequestStatus(requestId, status);
      // The real-time listener will update the UI automatically
    } catch (error: unknown) {
      console.error('Error updating request status:', error);
      setError('Failed to update request status');
    }
  };

  const handleUpdateMessageStatus = async (messageId: string, status: ContactMessage['status']) => {
    try {
      await updateContactMessageStatus(messageId, status);
      // The real-time listener will update the UI automatically
    } catch (error: unknown) {
      console.error('Error updating message status:', error);
      setError('Failed to update message status');
    }
  };

  if (!user || userData?.role !== 'admin') {
    return (
      <div className="container mx-auto px-4 py-20 text-center">
        <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
        <p className="text-gray-600 mb-4">You need admin privileges to access this page.</p>
        <Button asChild>
          <Link href="/">Go to Home</Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2 flex items-center">
                <Settings className="mr-3 h-8 w-8 text-blue-600" />
                Admin Dashboard
              </h1>
              <p className="text-gray-600">
                Manage your marketplace and monitor performance
              </p>
            </div>
            <div className="hidden md:flex items-center space-x-4">
              <Badge variant="outline" className="px-3 py-1">
                <Users className="w-4 h-4 mr-1" />
                {stats.totalUsers} Users
              </Badge>
              <Badge variant="outline" className="px-3 py-1">
                <FileText className="w-4 h-4 mr-1" />
                {stats.totalTemplates} Templates
              </Badge>
            </div>
          </div>
        </div>

      {/* Loading State */}
      {loading && (
        <div className="flex justify-center items-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading dashboard data...</p>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="mb-8 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-800">{error}</p>
        </div>
      )}

        {/* Stats Cards */}
        {!loading && (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
            {/* Templates Card */}
            <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white border-0">
              <CardContent className="p-4 sm:p-6">
                <div className="flex items-center justify-between">
                  <div className="min-w-0 flex-1">
                    <p className="text-blue-100 text-xs sm:text-sm font-medium mb-1">Templates</p>
                    <p className="text-2xl sm:text-3xl font-bold truncate">{stats.totalTemplates}</p>
                    <p className="text-blue-100 text-xs">Available templates</p>
                  </div>
                  <div className="p-2 sm:p-3 bg-white/20 rounded-lg flex-shrink-0 ml-3">
                    <FileText className="h-6 w-6 sm:h-8 sm:w-8 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Contact Requests Card */}
            <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white border-0">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-green-100 text-sm font-medium mb-1">Contact Requests</p>
                    <p className="text-3xl font-bold">{stats.totalRequests}</p>
                    <p className="text-green-100 text-xs">Customer inquiries</p>
                  </div>
                  <div className="p-3 bg-white/20 rounded-lg">
                    <MessageSquare className="h-8 w-8 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Total Sales Card */}
            <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white border-0">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-purple-100 text-sm font-medium mb-1">Total Sales</p>
                    <p className="text-3xl font-bold">{stats.pendingRequests}</p>
                    <p className="text-purple-100 text-xs">No revenue</p>
                  </div>
                  <div className="p-3 bg-white/20 rounded-lg">
                    <DollarSign className="h-8 w-8 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Customizations Card */}
            <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white border-0">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-orange-100 text-sm font-medium mb-1">Customizations</p>
                    <p className="text-3xl font-bold">{stats.customizations}</p>
                    <p className="text-orange-100 text-xs">Total customizations</p>
                  </div>
                  <div className="p-3 bg-white/20 rounded-lg">
                    <Palette className="h-8 w-8 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Users Card */}
            <Card className="bg-gradient-to-r from-cyan-500 to-cyan-600 text-white border-0">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-cyan-100 text-sm font-medium mb-1">Users</p>
                    <p className="text-3xl font-bold">{stats.totalUsers}</p>
                    <p className="text-cyan-100 text-xs">Site visitors</p>
                  </div>
                  <div className="p-3 bg-white/20 rounded-lg">
                    <Users className="h-8 w-8 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Navigation Tabs */}
        <div className="mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex flex-wrap gap-2 sm:gap-4 lg:gap-8">
              <button
                onClick={() => setActiveTab('templates')}
                className={`border-b-2 py-2 px-2 sm:px-3 text-xs sm:text-sm font-medium cursor-pointer whitespace-nowrap ${
                  activeTab === 'templates'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span className="hidden sm:inline">Templates Management</span>
                <span className="sm:hidden">Templates</span>
              </button>
              <button
                onClick={() => setActiveTab('requests')}
                className={`border-b-2 py-2 px-2 sm:px-3 text-xs sm:text-sm font-medium cursor-pointer whitespace-nowrap ${
                  activeTab === 'requests'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span className="hidden sm:inline">Contact Requests</span>
                <span className="sm:hidden">Contacts</span>
              </button>
              <button
                onClick={() => setActiveTab('purchases')}
                className={`border-b-2 py-2 px-2 sm:px-3 text-xs sm:text-sm font-medium cursor-pointer whitespace-nowrap ${
                  activeTab === 'purchases'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span className="hidden sm:inline">Purchase Requests</span>
                <span className="sm:hidden">Purchases</span>
              </button>
              <button
                onClick={() => setActiveTab('customizations')}
                className={`border-b-2 py-2 px-2 sm:px-3 text-xs sm:text-sm font-medium cursor-pointer whitespace-nowrap ${
                  activeTab === 'customizations'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span className="hidden sm:inline">Customizations</span>
                <span className="sm:hidden">Custom</span>
              </button>
              <button
                onClick={() => setActiveTab('chatbot')}
                className={`border-b-2 py-2 px-2 sm:px-3 text-xs sm:text-sm font-medium cursor-pointer whitespace-nowrap ${
                  activeTab === 'chatbot'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span className="hidden sm:inline">Chatbot Q&A</span>
                <span className="sm:hidden">Chatbot</span>
              </button>
              <button
                onClick={() => setActiveTab('careers')}
                className={`border-b-2 py-2 px-2 sm:px-3 text-xs sm:text-sm font-medium cursor-pointer whitespace-nowrap ${
                  activeTab === 'careers'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Careers
              </button>
              <button
                onClick={() => setActiveTab('internships')}
                className={`border-b-2 py-2 px-2 sm:px-3 text-xs sm:text-sm font-medium cursor-pointer whitespace-nowrap ${
                  activeTab === 'internships'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span className="hidden sm:inline">Internships</span>
                <span className="sm:hidden">Interns</span>
              </button>
              <button
                onClick={() => setActiveTab('reviews')}
                className={`border-b-2 py-2 px-2 sm:px-3 text-xs sm:text-sm font-medium cursor-pointer whitespace-nowrap ${
                  activeTab === 'reviews'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Reviews
              </button>
              <button
                onClick={() => setActiveTab('projects')}
                className={`border-b-2 py-2 px-2 sm:px-3 text-xs sm:text-sm font-medium cursor-pointer whitespace-nowrap ${
                  activeTab === 'projects'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span className="hidden sm:inline">Projects</span>
                <span className="sm:hidden">Work</span>
              </button>
              <button
                onClick={() => setActiveTab('team')}
                className={`border-b-2 py-2 px-2 sm:px-3 text-xs sm:text-sm font-medium cursor-pointer whitespace-nowrap ${
                  activeTab === 'team'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Team
              </button>
            </nav>
          </div>
        </div>

        {!loading && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
            {/* Content based on active tab */}
            <div className="lg:col-span-2 order-2 lg:order-1">
              {activeTab === 'templates' && (
                <TemplatesTab onRefresh={() => {
                  // Refresh dashboard stats if needed
                  console.log('Templates updated');
                }} />
              )}

              {activeTab === 'requests' && (
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle>Contact Requests</CardTitle>
                        <CardDescription>
                          Manage customer inquiries and requests
                        </CardDescription>
                      </div>
                      <div className="flex items-center gap-2">
                        <Filter className="h-4 w-4 text-gray-500" />
                        <Select value={statusFilter} onValueChange={setStatusFilter}>
                          <SelectTrigger className="w-40">
                            <SelectValue placeholder="Filter by status" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Status</SelectItem>
                            <SelectItem value="pending">Pending</SelectItem>
                            <SelectItem value="responded">Responded</SelectItem>
                            <SelectItem value="in-progress">In Progress</SelectItem>
                            <SelectItem value="resolved">Resolved</SelectItem>
                            <SelectItem value="declined">Declined</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {contactMessages
                        .filter(msg => msg.type === 'contact')
                        .filter(msg => statusFilter === 'all' || msg.status === statusFilter)
                        .length > 0 ? (
                        contactMessages
                          .filter(msg => msg.type === 'contact')
                          .filter(msg => statusFilter === 'all' || msg.status === statusFilter)
                          .map((message) => (
                          <div
                            key={message.id}
                            className="p-4 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
                            onClick={() => setSelectedRequest(message as ContactMessageType)}
                          >
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <div className="flex items-center gap-2 mb-2">
                                  <h4 className="font-medium text-gray-900">{message.userName}</h4>
                                  <Badge variant="secondary" className="text-xs">
                                    Contact Inquiry
                                  </Badge>
                                </div>
                                <p className="text-sm text-gray-600">{message.userEmail}</p>
                                {isContactMessage(message) && message.userPhone && (
                                  <p className="text-sm text-gray-600">{message.userPhone}</p>
                                )}
                                <p className="text-sm font-medium text-gray-800 mt-1">{message.subject}</p>
                                <p className="text-sm text-gray-500 mt-2 line-clamp-2">{message.message}</p>
                                {message.templateTitle && (
                                  <p className="text-xs text-blue-600 mt-1">Template: {message.templateTitle}</p>
                                )}
                                <p className="text-xs text-gray-400 mt-2">
                                  {getDateFromTimestamp(message.createdAt).toLocaleDateString()}
                                </p>
                              </div>
                              <div className="flex items-center ml-4" onClick={(e) => e.stopPropagation()}>
                                <StatusDropdown
                                  currentStatus={message.status}
                                  onStatusChange={(status) => handleUpdateMessageStatus(message.id, status as ContactMessage['status'])}
                                  type="contact-message"
                                />
                              </div>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="text-center py-8">
                          <MessageSquare className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                          <h3 className="text-lg font-semibold text-gray-900 mb-2">No contact requests</h3>
                          <p className="text-gray-600">
                            Contact requests from users will appear here.
                          </p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}

              {activeTab === 'purchases' && (
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle>Purchase Requests</CardTitle>
                        <CardDescription>
                          Manage template purchase requests from customers
                        </CardDescription>
                      </div>
                      <div className="flex items-center gap-2">
                        <Filter className="h-4 w-4 text-gray-500" />
                        <Select value={statusFilter} onValueChange={setStatusFilter}>
                          <SelectTrigger className="w-40">
                            <SelectValue placeholder="Filter by status" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Status</SelectItem>
                            <SelectItem value="pending">Pending</SelectItem>
                            <SelectItem value="confirmed">Confirmed</SelectItem>
                            <SelectItem value="approved">Order Approved</SelectItem>
                            <SelectItem value="completed">Completed</SelectItem>
                            <SelectItem value="declined">Declined</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {contactMessages
                        .filter(msg => msg.type === 'purchase-request')
                        .filter(msg => statusFilter === 'all' || msg.status === statusFilter)
                        .length > 0 ? (
                        contactMessages
                          .filter(msg => msg.type === 'purchase-request')
                          .filter(msg => statusFilter === 'all' || msg.status === statusFilter)
                          .map((message) => (
                          <div
                            key={message.id}
                            className="p-4 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
                            onClick={() => setSelectedRequest(message as ContactMessageType)}
                          >
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <div className="flex items-center gap-2 mb-2">
                                  <h4 className="font-medium text-gray-900">{message.userName}</h4>
                                  <Badge variant="default" className="text-xs">
                                    Purchase Request
                                  </Badge>
                                </div>
                                <p className="text-sm text-gray-600">{message.userEmail}</p>
                                {message.userPhone && (
                                  <p className="text-sm text-gray-600">{message.userPhone}</p>
                                )}
                                <p className="text-sm font-medium text-gray-800 mt-1">{message.subject}</p>
                                <p className="text-sm text-gray-500 mt-2 line-clamp-2">{message.message}</p>
                                {message.templateTitle && (
                                  <p className="text-xs text-blue-600 mt-1">Template: {message.templateTitle}</p>
                                )}
                                <p className="text-xs text-gray-400 mt-2">
                                  {getDateFromTimestamp(message.createdAt).toLocaleDateString()}
                                </p>
                              </div>
                              <div className="flex items-center ml-4" onClick={(e) => e.stopPropagation()}>
                                <StatusDropdown
                                  currentStatus={message.status}
                                  onStatusChange={(status) => handleUpdateMessageStatus(message.id, status as ContactMessage['status'])}
                                  type="purchase-request"
                                />
                              </div>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="text-center py-8">
                          <ShoppingCart className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                          <h3 className="text-lg font-semibold text-gray-900 mb-2">No purchase requests</h3>
                          <p className="text-gray-600">
                            Purchase requests from customers will appear here.
                          </p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}

              {activeTab === 'customizations' && (
                <Card>
                  <CardHeader>
                    <CardTitle>Customization Requests</CardTitle>
                    <CardDescription>
                      Manage custom template requests
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {customRequests.length > 0 ? (
                        customRequests.map((request) => (
                          <div key={request.id} className="p-4 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <div className="flex items-center gap-2 mb-2">
                                  <h4 className="font-medium text-gray-900">{request.title}</h4>
                                  {request.status === 'completed' || request.status === 'cancelled' ? (
                                    <Badge variant="default">Completed</Badge>
                                  ) : (
                                    <Badge variant="outline">Pending</Badge>
                                  )}
                                </div>
                                <p className="text-sm font-medium text-gray-900">{request.title}</p>
                                <p className="text-xs text-gray-500">{request.userEmail}</p>
                                <p className="text-sm text-gray-500 mt-2 line-clamp-2">{request.description}</p>
                                {request.budget && (
                                  <p className="text-xs text-gray-400 mt-2">Budget: ${request.budget}</p>
                                )}
                                <p className="text-xs text-gray-400">
                                  {getDateFromTimestamp(request.createdAt).toLocaleDateString()}
                                </p>
                              </div>
                              <div className="flex items-center ml-4">
                                <StatusDropdown
                                  currentStatus={request.status}
                                  onStatusChange={async (status) => {
                                    await handleUpdateRequestStatus(request.id, status as CustomRequest['status']);
                                  }}
                                  type="custom-request"
                                />
                              </div>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="text-center py-8">
                          <Palette className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                          <h3 className="text-lg font-semibold text-gray-900 mb-2">No customization requests</h3>
                          <p className="text-gray-600">
                            Custom template requests will appear here.
                          </p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}

              {activeTab === 'chatbot' && (
                <ChatbotTab onRefresh={() => {
                  // Refresh dashboard stats if needed
                  console.log('Chatbot Q&As updated');
                }} />
              )}

              {activeTab === 'careers' && (
                <CareersTab onRefresh={() => {
                  // Refresh dashboard stats if needed
                  console.log('Careers updated');
                }} />
              )}

              {activeTab === 'internships' && (
                <InternshipsTab onRefresh={() => {
                  // Refresh dashboard stats if needed
                  console.log('Internships updated');
                }} />
              )}

              {activeTab === 'reviews' && (
                <ReviewsTab onRefresh={() => {
                  // Refresh dashboard stats if needed
                  console.log('Reviews updated');
                }} />
              )}

              {activeTab === 'projects' && (
                <ProjectsTab onRefresh={() => {
                  // Refresh dashboard stats if needed
                  console.log('Projects updated');
                }} />
              )}

              {activeTab === 'team' && (
                <TeamTab />
              )}
            </div>

            {/* Quick Actions */}
            <div className="order-1 lg:order-2">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg sm:text-xl">Quick Actions</CardTitle>
                  <CardDescription>
                    Common admin tasks
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button
                    onClick={() => setActiveTab('templates')}
                    variant={activeTab === 'templates' ? 'default' : 'outline'}
                    className="w-full justify-start cursor-pointer text-sm"
                  >
                    <FileText className="mr-2 h-4 w-4 flex-shrink-0" />
                    <span className="truncate">Templates</span>
                  </Button>

                  <Button
                    variant="outline"
                    className="w-full justify-start cursor-pointer"
                    onClick={() => setActiveTab('requests')}
                  >
                    <MessageSquare className="mr-2 h-4 w-4" />
                    Contact Requests
                  </Button>

                  <Button asChild
                    variant="outline"
                    className="w-full justify-start cursor-pointer"
                  >
                    <Link href="/admin/purchase-requests">
                      <CreditCard className="mr-2 h-4 w-4" />
                      Purchase Requests
                    </Link>
                  </Button>

                  <Button asChild
                    variant="outline"
                    className="w-full justify-start cursor-pointer"
                  >
                    <Link href="/admin/custom-requests">
                      <Palette className="mr-2 h-4 w-4" />
                      Customizations
                    </Link>
                  </Button>

                  <Button
                    onClick={() => setActiveTab('reviews')}
                    variant={activeTab === 'reviews' ? 'default' : 'outline'}
                    className="w-full justify-start cursor-pointer"
                  >
                    <Star className="mr-2 h-4 w-4" />
                    Customer Reviews
                  </Button>

                  <Button
                    onClick={() => setActiveTab('team')}
                    variant={activeTab === 'team' ? 'default' : 'outline'}
                    className="w-full justify-start cursor-pointer"
                  >
                    <Users className="mr-2 h-4 w-4" />
                    Team Management
                  </Button>

                  <Button
                    onClick={() => setActiveTab('chatbot')}
                    variant={activeTab === 'chatbot' ? 'default' : 'outline'}
                    className="w-full justify-start cursor-pointer"
                  >
                    <Bot className="mr-2 h-4 w-4" />
                    Chatbot Q&A
                  </Button>

                  <Button
                    onClick={() => setActiveTab('careers')}
                    variant={activeTab === 'careers' ? 'default' : 'outline'}
                    className="w-full justify-start cursor-pointer"
                  >
                    <Briefcase className="mr-2 h-4 w-4" />
                    Careers
                  </Button>

                  <Button
                    onClick={() => setActiveTab('internships')}
                    variant={activeTab === 'internships' ? 'default' : 'outline'}
                    className="w-full justify-start cursor-pointer"
                  >
                    <Users className="mr-2 h-4 w-4" />
                    Internships
                  </Button>

                  <Button
                    onClick={() => setActiveTab('projects')}
                    variant={activeTab === 'projects' ? 'default' : 'outline'}
                    className="w-full justify-start cursor-pointer"
                  >
                    <Briefcase className="mr-2 h-4 w-4" />
                    Projects
                  </Button>
                </CardContent>
              </Card>

              {/* System Status */}
              <Card className="mt-6">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <BarChart3 className="mr-2 h-5 w-5" />
                    System Status
                  </CardTitle>
                  <CardDescription>
                    Current system health and statistics
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                      <div className="flex items-center">
                        <Database className="w-4 h-4 mr-2 text-green-600" />
                        <span className="text-sm font-medium text-gray-700">Firebase</span>
                      </div>
                      <Badge variant="default" className="bg-green-500 hover:bg-green-600">Connected</Badge>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                      <div className="flex items-center">
                        <Database className="w-4 h-4 mr-2 text-green-600" />
                        <span className="text-sm font-medium text-gray-700">Firestore</span>
                      </div>
                      <Badge variant="default" className="bg-green-500 hover:bg-green-600">Active</Badge>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                      <div className="flex items-center">
                        <Users className="w-4 h-4 mr-2 text-green-600" />
                        <span className="text-sm font-medium text-gray-700">Authentication</span>
                      </div>
                      <Badge variant="default" className="bg-green-500 hover:bg-green-600">Working</Badge>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                      <div className="flex items-center">
                        <FileText className="w-4 h-4 mr-2 text-blue-600" />
                        <span className="text-sm font-medium text-gray-700">Total Templates</span>
                      </div>
                      <span className="text-sm font-bold text-blue-600">{stats.totalTemplates}</span>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                      <div className="flex items-center">
                        <Users className="w-4 h-4 mr-2 text-purple-600" />
                        <span className="text-sm font-medium text-gray-700">Total Users</span>
                      </div>
                      <span className="text-sm font-bold text-purple-600">{stats.totalUsers}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}

        {/* Detailed Request View Dialog */}
        {selectedRequest && (
          <Dialog open={!!selectedRequest} onOpenChange={() => setSelectedRequest(null)}>
            <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Request Details</DialogTitle>
                <DialogDescription>
                  Complete information about this request
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-6">
                {/* User Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold border-b pb-2">User Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-600">Name</label>
                      <p className="text-sm text-gray-900">{selectedRequest.userName || selectedRequest.title}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">Email</label>
                      <p className="text-sm text-gray-900">{selectedRequest.userEmail}</p>
                    </div>
                    {selectedRequest.userPhone && (
                      <div>
                        <label className="text-sm font-medium text-gray-600">Phone</label>
                        <p className="text-sm text-gray-900">{selectedRequest.userPhone}</p>
                      </div>
                    )}
                    <div>
                      <label className="text-sm font-medium text-gray-600">Status</label>
                      <div className="mt-1">
                        <StatusDropdown
                          currentStatus={selectedRequest.status}
                          onStatusChange={(status) => {
                            if (isCustomRequest(selectedRequest)) {
                              handleUpdateRequestStatus(selectedRequest.id, status as CustomRequest['status']);
                            } else if (isContactMessage(selectedRequest)) {
                              handleUpdateMessageStatus(selectedRequest.id, status as ContactMessage['status']);
                            }
                          }}
                          type={isCustomRequest(selectedRequest) ? 'custom-request' : 'contact-message'}
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Request Details */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold border-b pb-2">Request Details</h3>
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium text-gray-600">Subject/Title</label>
                      <p className="text-sm text-gray-900">{selectedRequest.subject || selectedRequest.title}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">Description/Message</label>
                      <p className="text-sm text-gray-900 whitespace-pre-wrap">{selectedRequest.message || selectedRequest.description}</p>
                    </div>
                    {selectedRequest.templateTitle && (
                      <div>
                        <label className="text-sm font-medium text-gray-600">Template</label>
                        <p className="text-sm text-blue-600">{selectedRequest.templateTitle}</p>
                      </div>
                    )}
                    {selectedRequest.category && (
                      <div>
                        <label className="text-sm font-medium text-gray-600">Category</label>
                        <p className="text-sm text-gray-900">{selectedRequest.category}</p>
                      </div>
                    )}
                    {selectedRequest.budget && (
                      <div>
                        <label className="text-sm font-medium text-gray-600">Budget</label>
                        <p className="text-sm text-gray-900">₹{selectedRequest.budget}</p>
                      </div>
                    )}
                    {selectedRequest.deadline && (
                      <div>
                        <label className="text-sm font-medium text-gray-600">Deadline</label>
                        <p className="text-sm text-gray-900">{
                          hasSeconds((selectedRequest as any).deadline)
                            ? new Date((selectedRequest as any).deadline.seconds * 1000).toLocaleDateString()
                            : new Date((selectedRequest as any).deadline).toLocaleDateString()
                        }</p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Timestamps */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold border-b pb-2">Timeline</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-600">Created At</label>
                      <p className="text-sm text-gray-900">
                        {
                          isContactMessage(selectedRequest)
                            ? new Date(selectedRequest.createdAt.seconds ? selectedRequest.createdAt.seconds * 1000 : selectedRequest.createdAt).toLocaleString()
                            : new Date(selectedRequest.createdAt).toLocaleString()
                        }
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">Last Updated</label>
                      <p className="text-sm text-gray-900">
                        {
                          isContactMessage(selectedRequest)
                            ? new Date(selectedRequest.updatedAt && selectedRequest.updatedAt.seconds ? selectedRequest.updatedAt.seconds * 1000 : selectedRequest.updatedAt || selectedRequest.createdAt).toLocaleString()
                            : new Date(selectedRequest.updatedAt || selectedRequest.createdAt).toLocaleString()
                        }
                      </p>
                    </div>
                  </div>
                </div>

                {/* Admin Notes */}
                {selectedRequest.adminNotes && (
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold border-b pb-2">Admin Notes</h3>
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <p className="text-sm text-blue-800">{selectedRequest.adminNotes}</p>
                    </div>
                  </div>
                )}
              </div>
            </DialogContent>
          </Dialog>
        )}
      </div>
    </div>
  );
}

export default AdminDashboard;
